const express = require("express");
const { body, validationResult } = require("express-validator");
const Plugin = require("../models/Plugin");
const DownloadHistory = require("../models/DownloadHistory");
const { protect } = require("../middleware/auth");
const { fetchPluginData } = require("../services/wordpressAPI");

const router = express.Router();

// All routes are protected
router.use(protect);

// @desc    Get all plugins
// @route   GET /api/plugins
// @access  Private
router.get("/", async (req, res, next) => {
  try {
    const {
      search,
      status,
      page = 1,
      limit = 10,
      sortBy = "createdAt",
      sortOrder = "desc",
    } = req.query;

    // Build query
    let query = {};

    if (search) {
      query.$or = [
        { name: { $regex: search, $options: "i" } },
        { slug: { $regex: search, $options: "i" } },
        { description: { $regex: search, $options: "i" } },
      ];
    }

    if (status) {
      query.status = status;
    }

    // Build sort object
    const sort = {};
    sort[sortBy] = sortOrder === "asc" ? 1 : -1;

    // Execute query with pagination
    const plugins = await Plugin.find(query)
      .sort(sort)
      .limit(limit * 1)
      .skip((page - 1) * limit)
      .populate("addedBy", "name username");

    // Get total count for pagination
    const total = await Plugin.countDocuments(query);

    res.status(200).json({
      success: true,
      count: plugins.length,
      total,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        pages: Math.ceil(total / limit),
      },
      plugins,
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Get plugin statistics
// @route   GET /api/plugins/stats
// @access  Private
router.get("/stats", async (req, res, next) => {
  try {
    const stats = await Plugin.getStats();

    // Get recent plugins with enhanced data
    const recentPlugins = await Plugin.find()
      .sort({ createdAt: -1 })
      .limit(5)
      .select("name slug downloads rating createdAt pluginRank supportTickets");

    // Get download history for dashboard (past 2 days)
    const downloadHistory = await DownloadHistory.getRecentChanges(2);

    res.status(200).json({
      success: true,
      ...stats,
      recentPlugins,
      downloadHistory,
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Get dashboard data with plugin details and download history
// @route   GET /api/plugins/dashboard
// @access  Private
router.get("/dashboard", async (req, res, next) => {
  try {
    // Get all plugins with essential dashboard data
    const plugins = await Plugin.find({ status: "active" })
      .select(
        "name slug downloads rating pluginRank supportTickets lastDataFetch lastReleaseDate lastUpdated version"
      )
      .sort({ downloads: -1 })
      .limit(10);

    // Get download history for each plugin (past 2 days)
    const pluginsWithHistory = await Promise.all(
      plugins.map(async (plugin) => {
        const history = await DownloadHistory.getPluginHistory(plugin._id, 2);

        // Calculate download change
        let downloadChange = 0;
        if (history.length >= 2) {
          downloadChange = history[0].downloads - history[1].downloads;
        }

        return {
          _id: plugin._id,
          name: plugin.name,
          slug: plugin.slug,
          currentDownloads: plugin.downloads,
          currentRank: plugin.pluginRank,
          rating: plugin.rating,
          version: plugin.version,
          unresolvedTopics: plugin.supportTickets?.unresolvedTopics || 0,
          supportUrl: `https://wordpress.org/support/plugin/${plugin.slug}/unresolved/`,
          downloadHistory: history,
          downloadChange,
          lastDataFetch: plugin.lastDataFetch,
          lastReleaseDate: plugin.lastReleaseDate,
          lastUpdated: plugin.lastUpdated,
        };
      })
    );

    res.status(200).json({
      success: true,
      plugins: pluginsWithHistory,
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Get single plugin by ID
// @route   GET /api/plugins/:id
// @access  Private
router.get("/:id", async (req, res, next) => {
  try {
    const plugin = await Plugin.findById(req.params.id).populate(
      "addedBy",
      "name username"
    );

    if (!plugin) {
      return res.status(404).json({
        success: false,
        message: "Plugin not found",
      });
    }

    res.status(200).json({
      success: true,
      plugin,
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Get single plugin by slug
// @route   GET /api/plugins/slug/:slug
// @access  Private
router.get("/slug/:slug", async (req, res, next) => {
  try {
    const plugin = await Plugin.findOne({ slug: req.params.slug }).populate(
      "addedBy",
      "name username"
    );

    if (!plugin) {
      return res.status(404).json({
        success: false,
        message: "Plugin not found",
      });
    }

    res.status(200).json({
      success: true,
      plugin,
    });
  } catch (error) {
    next(error);
  }
});

// @desc    Add new plugin
// @route   POST /api/plugins
// @access  Private
router.post(
  "/",
  [
    body("slug").notEmpty().withMessage("Plugin slug is required"),
    body("name").notEmpty().withMessage("Plugin name is required"),
  ],
  async (req, res, next) => {
    try {
      // Check for validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: "Validation failed",
          errors: errors.array(),
        });
      }

      const { slug, name, description, tags, notes } = req.body;

      // Check if plugin already exists
      const existingPlugin = await Plugin.findOne({ slug });
      if (existingPlugin) {
        return res.status(400).json({
          success: false,
          message: "Plugin with this slug already exists",
        });
      }

      // Create plugin
      const plugin = await Plugin.create({
        slug,
        name,
        description,
        tags,
        notes,
        addedBy: req.user._id,
      });

      // Fetch initial data from WordPress.org
      try {
        const wpData = await fetchPluginData(slug);
        if (wpData) {
          Object.assign(plugin, wpData);
          await plugin.save();
        }
      } catch (fetchError) {
        console.error("Error fetching initial plugin data:", fetchError);
        // Continue even if fetch fails
      }

      await plugin.populate("addedBy", "name username");

      res.status(201).json({
        success: true,
        message: "Plugin added successfully",
        plugin,
      });
    } catch (error) {
      next(error);
    }
  }
);

// @desc    Update plugin
// @route   PUT /api/plugins/:id
// @access  Private
router.put(
  "/:id",
  [
    body("name")
      .optional()
      .notEmpty()
      .withMessage("Plugin name cannot be empty"),
  ],
  async (req, res, next) => {
    try {
      // Check for validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        return res.status(400).json({
          success: false,
          message: "Validation failed",
          errors: errors.array(),
        });
      }

      const { name, description, tags, notes } = req.body;

      const plugin = await Plugin.findById(req.params.id);

      if (!plugin) {
        return res.status(404).json({
          success: false,
          message: "Plugin not found",
        });
      }

      // Update fields
      if (name) plugin.name = name;
      if (description !== undefined) plugin.description = description;
      if (tags !== undefined) plugin.tags = tags;
      if (notes !== undefined) plugin.notes = notes;

      await plugin.save();
      await plugin.populate("addedBy", "name username");

      res.status(200).json({
        success: true,
        message: "Plugin updated successfully",
        plugin,
      });
    } catch (error) {
      next(error);
    }
  }
);

// @desc    Refresh plugin data from WordPress.org
// @route   POST /api/plugins/:id/refresh
// @access  Private
router.post("/:id/refresh", async (req, res, next) => {
  try {
    const plugin = await Plugin.findById(req.params.id);

    if (!plugin) {
      return res.status(404).json({
        success: false,
        message: "Plugin not found",
      });
    }

    // Fetch fresh data from WordPress.org
    const wpData = await fetchPluginData(plugin.slug);

    if (wpData) {
      Object.assign(plugin, wpData);
      plugin.lastDataFetch = new Date();
      await plugin.save();
    }

    await plugin.populate("addedBy", "name username");

    res.status(200).json({
      success: true,
      message: "Plugin data refreshed successfully",
      plugin,
    });
  } catch (error) {
    // Log the error but still return the plugin
    console.error("Error refreshing plugin data:", error);

    // Add error to plugin's fetch errors
    const plugin = await Plugin.findById(req.params.id);
    if (plugin) {
      plugin.fetchErrors.push({
        error: error.message,
        timestamp: new Date(),
      });
      await plugin.save();
    }

    res.status(500).json({
      success: false,
      message: "Failed to refresh plugin data",
      error: error.message,
    });
  }
});

// @desc    Delete plugin
// @route   DELETE /api/plugins/:id
// @access  Private
router.delete("/:id", async (req, res, next) => {
  try {
    const plugin = await Plugin.findById(req.params.id);

    if (!plugin) {
      return res.status(404).json({
        success: false,
        message: "Plugin not found",
      });
    }

    await Plugin.findByIdAndDelete(req.params.id);

    res.status(200).json({
      success: true,
      message: "Plugin deleted successfully",
    });
  } catch (error) {
    next(error);
  }
});

module.exports = router;
