import React, { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import {
  FiX,
  FiDownload,
  FiStar,
  FiBarChart2,
  FiPieChart,
  FiExternalLink,
} from "react-icons/fi";
import { wpAPI } from "../../services/api";
import { formatNumber } from "../../utils/formatters";

const PluginChartModal = ({ plugin, isOpen, onClose }) => {
  const [downloadStats, setDownloadStats] = useState(null);
  const [ratingData, setRatingData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [chartType, setChartType] = useState("downloads"); // "downloads" or "ratings"

  useEffect(() => {
    if (isOpen && plugin) {
      fetchPluginStats();
      fetchRatingData();
    }
  }, [isOpen, plugin]);

  const fetchPluginStats = async () => {
    setLoading(true);
    try {
      const response = await wpAPI.getPluginDownloadStats(plugin.slug);
      setDownloadStats(response.data.data);
    } catch (error) {
      console.error("Error fetching plugin stats:", error);
    } finally {
      setLoading(false);
    }
  };

  const fetchRatingData = async () => {
    try {
      // Fetch rating data directly from WordPress.org API
      const response = await fetch(
        `https://api.wordpress.org/plugins/info/1.2/?action=plugin_information&request[slug]=${plugin.slug}`
      );
      const data = await response.json();

      if (data && data.ratings) {
        const ratingBreakdown = {
          five: parseInt(data.ratings["5"]) || 0,
          four: parseInt(data.ratings["4"]) || 0,
          three: parseInt(data.ratings["3"]) || 0,
          two: parseInt(data.ratings["2"]) || 0,
          one: parseInt(data.ratings["1"]) || 0,
        };

        setRatingData({
          rating: data.rating
            ? (parseFloat(data.rating) / 20).toFixed(1)
            : "0.0",
          ratingCount: parseInt(data.num_ratings) || 0,
          ratingBreakdown: ratingBreakdown,
        });

        console.log("Fresh rating data fetched:", {
          plugin: plugin.name,
          rating: data.rating
            ? (parseFloat(data.rating) / 20).toFixed(1)
            : "0.0",
          ratingCount: parseInt(data.num_ratings) || 0,
          ratingBreakdown: ratingBreakdown,
        });
      }
    } catch (error) {
      console.error("Error fetching rating data:", error);
    }
  };

  const getDownloadChartData = () => {
    if (!downloadStats) return [];

    // Get last 15 days of data
    const entries = Object.entries(downloadStats)
      .sort(([a], [b]) => new Date(a) - new Date(b))
      .slice(-15);

    return entries.map(([date, downloads]) => ({
      date: new Date(date).toLocaleDateString("en-US", {
        month: "short",
        day: "numeric",
      }),
      downloads: parseInt(downloads) || 0,
    }));
  };

  const getRatingChartData = () => {
    // Use fresh rating data if available, otherwise fall back to plugin data
    const currentRatingData = ratingData || plugin;
    const ratingBreakdown = currentRatingData.ratingBreakdown || {};

    // Debug: Log the rating data to console
    console.log("Current rating data being used:", {
      plugin: plugin.name,
      source: ratingData ? "fresh API data" : "plugin prop data",
      rating: currentRatingData.rating,
      ratingCount: currentRatingData.ratingCount,
      ratingBreakdown: ratingBreakdown,
    });

    const chartData = [
      {
        name: "5 Stars",
        value: parseInt(ratingBreakdown.five) || 0,
        color: "#10B981",
      },
      {
        name: "4 Stars",
        value: parseInt(ratingBreakdown.four) || 0,
        color: "#34D399",
      },
      {
        name: "3 Stars",
        value: parseInt(ratingBreakdown.three) || 0,
        color: "#FCD34D",
      },
      {
        name: "2 Stars",
        value: parseInt(ratingBreakdown.two) || 0,
        color: "#F97316",
      },
      {
        name: "1 Star",
        value: parseInt(ratingBreakdown.one) || 0,
        color: "#EF4444",
      },
    ];

    // If no rating data exists, show a placeholder
    const hasRatingData = chartData.some((item) => item.value > 0);
    if (!hasRatingData) {
      return [
        {
          name: "No Ratings Yet",
          value: 1,
          color: "#E5E7EB",
        },
      ];
    }

    return chartData.filter((item) => item.value > 0); // Only show segments with values
  };

  const SimpleBarChart = ({ data }) => {
    const maxDownloads = Math.max(...data.map((d) => d.downloads));
    const minDownloads = Math.min(...data.map((d) => d.downloads));
    const avgDownloads = Math.round(
      data.reduce((sum, d) => sum + d.downloads, 0) / data.length
    );

    return (
      <div className="space-y-4">
        <div className="flex justify-between items-start">
          <div>
            <h4 className="text-lg font-semibold text-gray-900">
              Daily Downloads Trend
            </h4>
            <p className="text-xs text-gray-500 mt-1">
              Download values displayed above each bar
            </p>
          </div>
          <div className="text-right">
            <div className="text-sm text-gray-600 mb-2">Last 15 Days</div>
            <div className="flex flex-col space-y-1 text-xs">
              <div className="flex items-center">
                <span className="text-blue-600 font-medium">Peak:</span>
                <span className="ml-2 font-semibold text-blue-900">
                  {maxDownloads.toLocaleString()}
                </span>
              </div>
              <div className="flex items-center">
                <span className="text-green-600 font-medium">Avg:</span>
                <span className="ml-2 font-semibold text-green-900">
                  {avgDownloads.toLocaleString()}
                </span>
              </div>
              <div className="flex items-center">
                <span className="text-gray-600 font-medium">Min:</span>
                <span className="ml-2 font-semibold text-gray-900">
                  {minDownloads.toLocaleString()}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Chart */}
        <div className="bg-gray-50 rounded-lg p-4">
          <div
            className="flex items-end justify-center space-x-2 relative"
            style={{ height: "280px", paddingBottom: "40px" }}
          >
            {/* Grid Lines */}
            <div className="absolute inset-0" style={{ paddingBottom: "40px" }}>
              {/* Horizontal Grid Lines */}
              {[0, 25, 50, 75, 100].map((percentage) => (
                <div
                  key={`h-${percentage}`}
                  className="absolute w-full border-t border-gray-300/50"
                  style={{
                    bottom: `${(percentage / 100) * 200 + 40}px`,
                  }}
                />
              ))}

              {/* Vertical Grid Lines */}
              {data.map((_, index) => (
                <div
                  key={`v-${index}`}
                  className="absolute h-full border-l border-gray-300/30"
                  style={{
                    left: `${((index + 0.5) / data.length) * 100}%`,
                  }}
                />
              ))}
            </div>

            {data.map((item, index) => {
              const barHeight = (item.downloads / maxDownloads) * 200;
              const formattedDownloads = item.downloads.toLocaleString();

              return (
                <div
                  key={index}
                  className="flex flex-col items-center group relative z-10"
                  style={{ minWidth: "calc(100% / 15 - 8px)" }}
                >
                  {/* Data Label (always visible) */}
                  <div
                    className="absolute text-xs font-medium text-gray-700 z-20 pointer-events-none"
                    style={{
                      bottom: `${barHeight + 5}px`,
                      transform: "translateX(-50%)",
                      left: "50%",
                      minWidth: "max-content",
                      whiteSpace: "nowrap",
                    }}
                  >
                    {formattedDownloads}
                  </div>

                  <div
                    className="bg-gradient-to-t from-blue-600 to-blue-400 rounded-t-sm transition-all hover:from-blue-700 hover:to-blue-500 cursor-pointer shadow-sm w-full relative z-10"
                    style={{
                      height: `${barHeight}px`,
                      minHeight: "3px",
                      maxWidth: "24px",
                    }}
                    title={`${
                      item.date
                    }: ${item.downloads.toLocaleString()} downloads`}
                  />
                  <div
                    className="absolute text-xs text-gray-500 group-hover:text-gray-700 whitespace-nowrap z-10"
                    style={{
                      bottom: "-35px",
                      transform: "translateX(-50%) rotate(-45deg)",
                      left: "50%",
                      transformOrigin: "center center",
                    }}
                  >
                    {item.date}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    );
  };

  const SimplePieChart = ({ data }) => {
    const total = data.reduce((sum, item) => sum + item.value, 0);
    let currentAngle = 0;

    return (
      <div className="space-y-4">
        <h4 className="text-lg font-semibold text-gray-900 mb-4">
          Rating Distribution
        </h4>
        <div className="flex flex-col lg:flex-row items-center gap-8">
          {/* Pie Chart */}
          <div className="relative">
            <svg width="240" height="240" className="transform -rotate-90">
              {/* Grid Lines for Pie Chart */}
              <g className="opacity-20">
                {/* Concentric Circles */}
                {[25, 50, 75, 100].map((radius) => (
                  <circle
                    key={`circle-${radius}`}
                    cx="120"
                    cy="120"
                    r={radius}
                    fill="none"
                    stroke="#6B7280"
                    strokeWidth="1"
                    strokeDasharray="2,2"
                  />
                ))}

                {/* Radial Lines */}
                {[0, 45, 90, 135, 180, 225, 270, 315].map((angle) => {
                  const x = 120 + 100 * Math.cos((angle * Math.PI) / 180);
                  const y = 120 + 100 * Math.sin((angle * Math.PI) / 180);
                  return (
                    <line
                      key={`line-${angle}`}
                      x1="120"
                      y1="120"
                      x2={x}
                      y2={y}
                      stroke="#6B7280"
                      strokeWidth="1"
                      strokeDasharray="2,2"
                    />
                  );
                })}
              </g>

              {data.map((item, index) => {
                const percentage = (item.value / total) * 100;
                const angle = (item.value / total) * 360;
                const startAngle = currentAngle;
                const endAngle = currentAngle + angle;

                const x1 = 120 + 100 * Math.cos((startAngle * Math.PI) / 180);
                const y1 = 120 + 100 * Math.sin((startAngle * Math.PI) / 180);
                const x2 = 120 + 100 * Math.cos((endAngle * Math.PI) / 180);
                const y2 = 120 + 100 * Math.sin((endAngle * Math.PI) / 180);

                const largeArcFlag = angle > 180 ? 1 : 0;

                const pathData = [
                  `M 120 120`,
                  `L ${x1} ${y1}`,
                  `A 100 100 0 ${largeArcFlag} 1 ${x2} ${y2}`,
                  `Z`,
                ].join(" ");

                // Calculate label position
                const labelAngle = startAngle + angle / 2;
                const labelRadius = 70;
                const labelX =
                  120 + labelRadius * Math.cos((labelAngle * Math.PI) / 180);
                const labelY =
                  120 + labelRadius * Math.sin((labelAngle * Math.PI) / 180);

                currentAngle += angle;

                return (
                  <g key={index}>
                    <path
                      d={pathData}
                      fill={item.color}
                      stroke="white"
                      strokeWidth="3"
                      className="hover:opacity-80 transition-opacity cursor-pointer"
                    />
                    {/* Data label on pie slice */}
                    {percentage > 8 && ( // Only show label if segment is large enough
                      <text
                        x={labelX}
                        y={labelY}
                        textAnchor="middle"
                        dominantBaseline="middle"
                        className="text-xs font-bold fill-white"
                        style={{
                          fontSize: "11px",
                          textShadow: "1px 1px 2px rgba(0,0,0,0.7)",
                          transform: "rotate(90deg)",
                          transformOrigin: `${labelX}px ${labelY}px`,
                        }}
                      >
                        {percentage.toFixed(0)}%
                      </text>
                    )}
                  </g>
                );
              })}
            </svg>

            {/* Center content */}
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="text-center bg-white rounded-full w-20 h-20 flex items-center justify-center shadow-lg">
                <div>
                  <div className="text-lg font-bold text-gray-900">
                    {(ratingData || plugin).rating || "0.0"}
                  </div>
                  <div className="text-xs text-gray-500">
                    {(ratingData || plugin).ratingCount || 0} reviews
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Legend */}
          <div className="space-y-3">
            <h5 className="font-medium text-gray-900">Rating Breakdown</h5>
            {data.map((item, index) => {
              const percentage = ((item.value / total) * 100).toFixed(1);
              return (
                <div
                  key={index}
                  className="flex items-center justify-between min-w-[200px]"
                >
                  <div className="flex items-center space-x-3">
                    <div
                      className="w-4 h-4 rounded-full"
                      style={{ backgroundColor: item.color }}
                    />
                    <span className="text-sm font-medium text-gray-700">
                      {item.name}
                    </span>
                  </div>
                  <div className="text-sm text-gray-600">
                    {item.value} ({percentage}%)
                  </div>
                </div>
              );
            })}

            {/* Total ratings */}
            <div className="pt-3 border-t border-gray-200">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-700">
                  Total Ratings
                </span>
                <span className="text-sm font-bold text-gray-900">
                  {total.toLocaleString()}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* Modal Header */}
        <div className="flex justify-between items-center p-6 border-b">
          <div className="flex-1 min-w-0 mr-4">
            <div className="relative max-w-[280px]">
              <h2
                className="text-xl font-semibold text-gray-900 overflow-hidden whitespace-nowrap"
                title={plugin?.name}
              >
                {plugin?.name}
              </h2>
              {/* Fade overlay for long names */}
              <div className="absolute top-0 right-0 h-full w-12 bg-gradient-to-l from-white via-white/80 to-transparent pointer-events-none" />
            </div>
            <p className="text-sm text-gray-600 mt-1">
              Download trends and rating distribution
            </p>
          </div>

          <div className="flex items-center space-x-2">
            {/* Plugin Details Button */}
            <a
              href={`https://wordpress.org/plugins/${plugin?.slug}/`}
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center px-4 py-2 text-sm bg-green-100 text-green-700 hover:bg-green-200 rounded-lg transition-colors border border-green-300 whitespace-nowrap min-w-[120px] justify-center"
            >
              <FiExternalLink className="w-4 h-4 mr-2" />
              Plugin Details
            </a>

            {/* Full Analytics Button */}
            <Link
              to={`/analytics?plugin=${plugin?._id}`}
              className="flex items-center px-4 py-2 text-sm bg-purple-100 text-purple-700 hover:bg-purple-200 rounded-lg transition-colors border border-purple-300 whitespace-nowrap min-w-[120px] justify-center"
            >
              <FiBarChart2 className="w-4 h-4 mr-2" />
              Full Analytics
            </Link>

            {/* Close Button */}
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors p-2 hover:bg-gray-100 rounded-lg"
            >
              <FiX className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* Combined Chart Content */}
        <div className="p-6">
          {loading ? (
            <div className="flex items-center justify-center h-64">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
            </div>
          ) : (
            <div className="space-y-8">
              {/* Download Chart */}
              <div>
                <SimpleBarChart data={getDownloadChartData()} />
              </div>

              {/* Divider */}
              <div className="border-t border-gray-200"></div>

              {/* Rating Chart */}
              <div>
                <SimplePieChart data={getRatingChartData()} />
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default PluginChartModal;
