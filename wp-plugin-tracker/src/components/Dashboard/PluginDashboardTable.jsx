import React, { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import {
  FiTrendingUp,
  FiExternalLink,
  FiMessageCircle,
  FiRefreshCw,
  FiCalendar,
  FiDownload,
  FiStar,
} from "react-icons/fi";
import { pluginsAPI } from "../../services/api";
import { formatNumber, formatRelativeTime } from "../../utils/formatters";
import PluginIcon from "../Common/PluginIcon";
import { toast } from "react-hot-toast";

const PluginDashboardTable = () => {
  const [plugins, setPlugins] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async (isRefresh = false) => {
    try {
      const response = await pluginsAPI.getDashboardData();
      setPlugins(response.data.plugins);
    } catch (error) {
      console.error("Error fetching dashboard data:", error);
      if (isRefresh) {
        toast.error("Failed to refresh dashboard data");
      }
    } finally {
      if (!isRefresh) {
        setLoading(false);
      }
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await fetchDashboardData(true);
      toast.success("Dashboard data refreshed successfully!");
    } catch (error) {
      console.error("Error refreshing dashboard data:", error);
      toast.error("Failed to refresh dashboard data");
    } finally {
      setRefreshing(false);
    }
  };

  // Helper function to extract date and calculate days
  const getLastReleaseInfo = (plugin) => {
    // Try to get from new fields first
    if (plugin.lastReleaseDate) {
      try {
        const date = new Date(plugin.lastReleaseDate);
        if (!isNaN(date.getTime())) {
          const dateStr = date.toISOString().split("T")[0]; // Get YYYY-MM-DD format
          const now = new Date();
          const diffTime = Math.abs(now - date);
          const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
          return { date: dateStr, days: diffDays };
        }
      } catch (error) {
        // Continue to fallback
      }
    }

    // Try to extract from lastUpdated field (WordPress.org format)
    if (plugin.lastUpdated) {
      try {
        // Extract date from WordPress.org format like "2025-05-14 10:31am GMT"
        const match = plugin.lastUpdated.match(/(\d{4}-\d{2}-\d{2})/);
        if (match) {
          const dateStr = match[1]; // This gives us "2025-05-14"
          const date = new Date(dateStr + "T00:00:00.000Z");
          const now = new Date();
          const diffTime = Math.abs(now - date);
          const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
          return { date: dateStr, days: diffDays };
        }
      } catch (error) {
        // Continue to fallback
      }
    }

    return { date: null, days: 0 };
  };

  if (loading) {
    return (
      <div className="card">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="card">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h2 className="text-lg font-semibold text-gray-900">
            Plugin Performance Dashboard
          </h2>
          <p className="text-sm text-gray-600">
            Track your plugins' downloads, rankings, and support metrics
          </p>
        </div>
        <button
          onClick={handleRefresh}
          disabled={refreshing}
          className="btn-secondary flex items-center"
        >
          <FiRefreshCw
            className={`w-4 h-4 mr-2 ${refreshing ? "animate-spin" : ""}`}
          />
          Refresh
        </button>
      </div>

      {plugins.length === 0 ? (
        <div className="text-center py-8">
          <p className="text-gray-500">No active plugins found</p>
        </div>
      ) : (
        <>
          {/* Mobile Card View */}
          <div className="block sm:hidden space-y-4">
            {plugins.map((plugin) => (
              <div key={plugin._id} className="bg-gray-50 rounded-lg p-4">
                <div className="flex items-center mb-3">
                  <PluginIcon plugin={plugin} size="sm" className="mr-3" />
                  <div className="flex-1 min-w-0">
                    <h3
                      className="text-sm font-medium text-gray-900 truncate"
                      title={plugin.name}
                    >
                      {plugin.name}
                    </h3>
                    <p
                      className="text-xs text-gray-500 truncate"
                      title={plugin.slug}
                    >
                      {plugin.slug}
                    </p>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-3 text-xs">
                  <div>
                    <span className="text-gray-500">Rank:</span>
                    <span className="ml-1 font-medium">
                      #{plugin.currentRank || "N/A"}
                    </span>
                  </div>
                  <div>
                    <span className="text-gray-500">Downloads:</span>
                    <span className="ml-1 font-medium">
                      {formatNumber(plugin.currentDownloads)}
                    </span>
                  </div>
                  <div>
                    <span className="text-gray-500">Rating:</span>
                    <span className="ml-1 font-medium">
                      {plugin.rating || "N/A"}
                    </span>
                  </div>
                  <div>
                    <span className="text-gray-500">Version:</span>
                    <span className="ml-1 font-medium">
                      {plugin.version || plugin.latestVersion || "N/A"}
                    </span>
                  </div>
                  <div className="col-span-2">
                    <span className="text-gray-500">Last Release:</span>
                    <span className="ml-1 font-medium">
                      {(() => {
                        const releaseInfo = getLastReleaseInfo(plugin);
                        return releaseInfo.date
                          ? `${releaseInfo.date} (${releaseInfo.days} days ago)`
                          : "Never";
                      })()}
                    </span>
                  </div>
                  <div className="col-span-2">
                    <span className="text-gray-500">Support:</span>
                    <a
                      href={plugin.supportUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="ml-1 text-primary-600 font-medium inline-flex items-center"
                      title={`View unresolved support topics: ${plugin.supportUrl}`}
                    >
                      {plugin.unresolvedTopics}
                      <FiExternalLink className="w-3 h-3 ml-1" />
                    </a>
                  </div>
                </div>
                <div className="mt-3 pt-3 border-t border-gray-200">
                  <Link
                    to={`/plugins/${plugin.slug}`}
                    className="text-xs text-primary-600 hover:text-primary-700 inline-flex items-center"
                    title="View plugin details"
                  >
                    <FiExternalLink className="w-3 h-3 mr-1" />
                    View Plugin Details
                  </Link>
                </div>
              </div>
            ))}
          </div>

          {/* Desktop Table View */}
          <div className="hidden sm:block">
            <table className="w-full table-fixed divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th
                    className="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    style={{ width: "22%" }}
                  >
                    Plugin
                  </th>
                  <th
                    className="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    style={{ width: "10%" }}
                  >
                    Rank
                  </th>
                  <th
                    className="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    style={{ width: "12%" }}
                  >
                    Downloads
                  </th>
                  <th
                    className="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    style={{ width: "8%" }}
                  >
                    Rating
                  </th>
                  <th
                    className="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    style={{ width: "10%" }}
                  >
                    Version
                  </th>
                  <th
                    className="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    style={{ width: "20%" }}
                  >
                    Last Release
                  </th>
                  <th
                    className="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    style={{ width: "18%" }}
                  >
                    Support
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {plugins.map((plugin) => (
                  <tr key={plugin._id} className="hover:bg-gray-50">
                    <td className="px-2 py-3">
                      <div className="flex items-center min-w-0">
                        <PluginIcon
                          plugin={plugin}
                          size="sm"
                          className="mr-2 flex-shrink-0"
                        />
                        <div className="min-w-0 flex-1">
                          <div
                            className="text-sm font-medium text-gray-900 truncate"
                            title={plugin.name}
                          >
                            {plugin.name}
                          </div>
                          <div className="flex items-center justify-between">
                            <div
                              className="text-xs text-gray-500 truncate"
                              title={plugin.slug}
                            >
                              {plugin.slug}
                            </div>
                            <Link
                              to={`/plugins/${plugin.slug}`}
                              className="text-xs text-primary-600 hover:text-primary-700 inline-flex items-center ml-2 flex-shrink-0"
                              title="View plugin details"
                            >
                              <FiExternalLink className="w-3 h-3" />
                            </Link>
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-2 py-3 whitespace-nowrap">
                      <div className="flex items-center">
                        <FiTrendingUp className="w-3 h-3 text-purple-500 mr-1" />
                        <span className="text-xs font-medium text-gray-900">
                          #{plugin.currentRank || "N/A"}
                        </span>
                      </div>
                    </td>
                    <td className="px-2 py-3 whitespace-nowrap">
                      <div className="flex items-center">
                        <FiDownload className="w-3 h-3 text-blue-600 mr-1" />
                        <span className="text-xs font-medium text-gray-900">
                          {formatNumber(plugin.currentDownloads)}
                        </span>
                      </div>
                    </td>
                    <td className="px-2 py-3 whitespace-nowrap">
                      <div className="flex items-center">
                        <FiStar className="w-3 h-3 text-yellow-600 mr-1" />
                        <span className="text-xs font-medium text-gray-900">
                          {plugin.rating || "N/A"}
                        </span>
                      </div>
                    </td>
                    <td className="px-2 py-3 whitespace-nowrap">
                      <div className="flex items-center">
                        <span className="text-xs font-medium text-gray-900">
                          {plugin.version || plugin.latestVersion || "N/A"}
                        </span>
                      </div>
                    </td>
                    <td className="px-2 py-3">
                      <div className="flex items-center">
                        <FiCalendar className="w-3 h-3 text-green-500 mr-1 flex-shrink-0" />
                        <div className="min-w-0">
                          {(() => {
                            const releaseInfo = getLastReleaseInfo(plugin);
                            return (
                              <>
                                <div className="text-xs font-medium text-gray-900 truncate">
                                  {releaseInfo.date || "Never"}
                                </div>
                                <div className="text-xs text-gray-500 truncate">
                                  {releaseInfo.date && releaseInfo.days > 0
                                    ? `${releaseInfo.days}d ago`
                                    : ""}
                                </div>
                              </>
                            );
                          })()}
                        </div>
                      </div>
                    </td>
                    <td className="px-2 py-3">
                      <a
                        href={plugin.supportUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex items-center text-primary-600"
                        title={`View unresolved support topics: ${plugin.supportUrl}`}
                      >
                        <FiMessageCircle
                          className={`w-3 h-3 mr-1 ${
                            plugin.unresolvedTopics > 0
                              ? "text-red-500"
                              : "text-gray-400"
                          }`}
                        />
                        <span className="text-xs font-medium truncate">
                          {plugin.unresolvedTopics}
                        </span>
                        <FiExternalLink className="w-3 h-3 ml-1 flex-shrink-0" />
                      </a>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </>
      )}
    </div>
  );
};

export default PluginDashboardTable;
