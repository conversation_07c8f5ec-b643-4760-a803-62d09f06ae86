import React from "react";
import { Link } from "react-router-dom";
import { FiPlus } from "react-icons/fi";
import { useAuth } from "../../contexts/AuthContext";
import PluginDashboardTable from "./PluginDashboardTable";
import PluginPerformanceCards from "./PluginPerformanceCards";
import DownloadTracking from "./DownloadTracking";

const Dashboard = () => {
  const { user } = useAuth();

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
          <p className="text-gray-600">Welcome back, {user?.name}!</p>
        </div>
        <Link to="/plugins" className="btn-primary flex items-center">
          <FiPlus className="w-4 h-4 mr-2" />
          Add Plugin
        </Link>
      </div>

      {/* Plugin Performance Dashboard - 8 columns */}
      <div className="mb-6">
        <div className="grid grid-cols-12 gap-6">
          <div className="col-span-12 lg:col-span-8">
            <PluginDashboardTable />
          </div>
        </div>
      </div>

      {/* Plugin Performance Cards - Full width */}
      <div className="mb-6">
        <PluginPerformanceCards />
      </div>

      {/* Download Tracking - Full width (12 columns) */}
      <div className="w-full">
        <DownloadTracking />
      </div>
    </div>
  );
};

export default Dashboard;
