import React, { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import {
  FiTrendingUp,
  FiTrendingDown,
  FiBarChart3,
  FiPieChart,
  FiCalendar,
  FiTag,
  FiRefreshCw,
} from "react-icons/fi";
import { pluginsAPI, wpAPI } from "../../services/api";
import { formatNumber, formatRelativeTime } from "../../utils/formatters";
import PluginIcon from "../Common/PluginIcon";
import PluginChartModal from "./PluginChartModal";
import { toast } from "react-hot-toast";

const PluginPerformanceCards = () => {
  const [plugins, setPlugins] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedPlugin, setSelectedPlugin] = useState(null);
  const [showChartModal, setShowChartModal] = useState(false);
  const [chartType, setChartType] = useState("downloads");

  useEffect(() => {
    fetchPluginData();
  }, []);

  const fetchPluginData = async (isRefresh = false) => {
    try {
      const response = await pluginsAPI.getDashboardData();
      const pluginsWithDownloadData = await Promise.all(
        response.data.plugins.map(async (plugin) => {
          try {
            // Fetch download stats for yesterday and day before yesterday
            const downloadStats = await fetchDownloadStats(plugin.slug);
            return {
              ...plugin,
              downloadStats,
            };
          } catch (error) {
            console.error(`Error fetching download stats for ${plugin.name}:`, error);
            return {
              ...plugin,
              downloadStats: null,
            };
          }
        })
      );
      setPlugins(pluginsWithDownloadData);
    } catch (error) {
      console.error("Error fetching plugin data:", error);
      if (isRefresh) {
        toast.error("Failed to refresh plugin data");
      }
    } finally {
      if (!isRefresh) {
        setLoading(false);
      }
    }
  };

  const fetchDownloadStats = async (slug) => {
    try {
      const response = await wpAPI.getPluginDownloadStats(slug);
      const downloadData = response.data.data;
      
      if (!downloadData || typeof downloadData !== 'object') {
        return null;
      }

      // Get yesterday and day before yesterday dates
      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);
      const dayBeforeYesterday = new Date();
      dayBeforeYesterday.setDate(dayBeforeYesterday.getDate() - 2);

      const yesterdayKey = yesterday.toISOString().split('T')[0];
      const dayBeforeYesterdayKey = dayBeforeYesterday.toISOString().split('T')[0];

      const yesterdayDownloads = parseInt(downloadData[yesterdayKey]) || 0;
      const dayBeforeYesterdayDownloads = parseInt(downloadData[dayBeforeYesterdayKey]) || 0;
      const change = yesterdayDownloads - dayBeforeYesterdayDownloads;

      return {
        yesterday: yesterdayDownloads,
        dayBeforeYesterday: dayBeforeYesterdayDownloads,
        change,
        changePercentage: dayBeforeYesterdayDownloads > 0 
          ? ((change / dayBeforeYesterdayDownloads) * 100).toFixed(1)
          : 0,
      };
    } catch (error) {
      console.error(`Error fetching download stats for ${slug}:`, error);
      return null;
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await fetchPluginData(true);
      toast.success("Plugin performance data refreshed successfully!");
    } catch (error) {
      console.error("Error refreshing plugin data:", error);
      toast.error("Failed to refresh plugin data");
    } finally {
      setRefreshing(false);
    }
  };

  const handleViewChart = (plugin, type) => {
    setSelectedPlugin(plugin);
    setChartType(type);
    setShowChartModal(true);
  };

  const getLastReleaseInfo = (plugin) => {
    // Try to get from new fields first
    if (plugin.lastReleaseDate) {
      try {
        const date = new Date(plugin.lastReleaseDate);
        if (!isNaN(date.getTime())) {
          const dateStr = date.toISOString().split("T")[0];
          const now = new Date();
          const diffTime = Math.abs(now - date);
          const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
          return { date: dateStr, days: diffDays };
        }
      } catch (error) {
        // Continue to fallback
      }
    }

    // Try to extract from lastUpdated field
    if (plugin.lastUpdated) {
      try {
        const match = plugin.lastUpdated.match(/(\d{4}-\d{2}-\d{2})/);
        if (match) {
          const dateStr = match[1];
          const date = new Date(dateStr + "T00:00:00.000Z");
          const now = new Date();
          const diffTime = Math.abs(now - date);
          const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
          return { date: dateStr, days: diffDays };
        }
      } catch (error) {
        // Continue to fallback
      }
    }

    return { date: null, days: 0 };
  };

  if (loading) {
    return (
      <div className="card">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Section Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-lg font-semibold text-gray-900">
            Plugin Performance Cards
          </h2>
          <p className="text-sm text-gray-600">
            Track plugin performance with download trends and key metrics
          </p>
        </div>
        <button
          onClick={handleRefresh}
          disabled={refreshing}
          className="btn-secondary flex items-center"
        >
          <FiRefreshCw
            className={`w-4 h-4 mr-2 ${refreshing ? "animate-spin" : ""}`}
          />
          Refresh
        </button>
      </div>

      {/* Plugin Cards Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {plugins.map((plugin) => {
          const releaseInfo = getLastReleaseInfo(plugin);
          const downloadStats = plugin.downloadStats;
          
          return (
            <div key={plugin._id} className="bg-white rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
              {/* Top Part - Plugin Info */}
              <div className="p-4 border-b border-gray-100">
                <div className="flex items-start space-x-3">
                  <PluginIcon plugin={plugin} size="sm" className="flex-shrink-0" />
                  <div className="flex-1 min-w-0">
                    <h3 className="text-sm font-semibold text-gray-900 truncate" title={plugin.name}>
                      {plugin.name}
                    </h3>
                    <div className="mt-1 space-y-1">
                      <div className="flex items-center justify-between text-xs">
                        <span className="text-gray-500">Rank:</span>
                        <span className="font-medium text-purple-600">
                          #{plugin.currentRank || "N/A"}
                        </span>
                      </div>
                      <div className="flex items-center justify-between text-xs">
                        <span className="text-gray-500">Version:</span>
                        <span className="font-medium text-blue-600">
                          {plugin.version || "N/A"}
                        </span>
                      </div>
                      <div className="flex items-center justify-between text-xs">
                        <span className="text-gray-500">Last Release:</span>
                        <div className="text-right">
                          <div className="font-medium text-green-600">
                            {releaseInfo.date || "Never"}
                          </div>
                          {releaseInfo.date && (
                            <div className="text-gray-400">
                              {releaseInfo.days}d ago
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Middle Part - Download Stats */}
              <div className="p-4 border-b border-gray-100">
                <h4 className="text-xs font-medium text-gray-700 mb-3">Download Trends</h4>
                {downloadStats ? (
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-xs text-gray-500">Yesterday:</span>
                      <span className="text-sm font-semibold text-gray-900">
                        {formatNumber(downloadStats.yesterday)}
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-xs text-gray-500">Day Before:</span>
                      <span className="text-sm font-medium text-gray-700">
                        {formatNumber(downloadStats.dayBeforeYesterday)}
                      </span>
                    </div>
                    <div className="flex items-center justify-between pt-1 border-t border-gray-100">
                      <span className="text-xs text-gray-500">Change:</span>
                      <div className="flex items-center space-x-1">
                        {downloadStats.change > 0 ? (
                          <FiTrendingUp className="w-3 h-3 text-green-500" />
                        ) : downloadStats.change < 0 ? (
                          <FiTrendingDown className="w-3 h-3 text-red-500" />
                        ) : null}
                        <span className={`text-xs font-medium ${
                          downloadStats.change > 0 
                            ? 'text-green-600' 
                            : downloadStats.change < 0 
                            ? 'text-red-600' 
                            : 'text-gray-600'
                        }`}>
                          {downloadStats.change > 0 ? '+' : ''}{formatNumber(downloadStats.change)}
                        </span>
                        <span className={`text-xs ${
                          downloadStats.change > 0 
                            ? 'text-green-500' 
                            : downloadStats.change < 0 
                            ? 'text-red-500' 
                            : 'text-gray-500'
                        }`}>
                          ({downloadStats.changePercentage > 0 ? '+' : ''}{downloadStats.changePercentage}%)
                        </span>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-2">
                    <p className="text-xs text-gray-400">No download data available</p>
                  </div>
                )}
              </div>

              {/* Bottom Part - Action Buttons */}
              <div className="p-3">
                <div className="flex space-x-2">
                  <button
                    onClick={() => handleViewChart(plugin, 'downloads')}
                    className="flex-1 flex items-center justify-center px-3 py-2 text-xs font-medium text-blue-600 bg-blue-50 hover:bg-blue-100 rounded-md transition-colors"
                  >
                    <FiBarChart3 className="w-3 h-3 mr-1" />
                    Charts
                  </button>
                  <Link
                    to={`/analytics?plugin=${plugin._id}`}
                    className="flex-1 flex items-center justify-center px-3 py-2 text-xs font-medium text-purple-600 bg-purple-50 hover:bg-purple-100 rounded-md transition-colors"
                  >
                    <FiPieChart className="w-3 h-3 mr-1" />
                    Analytics
                  </Link>
                </div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Chart Modal */}
      {showChartModal && selectedPlugin && (
        <PluginChartModal
          plugin={selectedPlugin}
          chartType={chartType}
          onClose={() => {
            setShowChartModal(false);
            setSelectedPlugin(null);
          }}
        />
      )}
    </div>
  );
};

export default PluginPerformanceCards;
